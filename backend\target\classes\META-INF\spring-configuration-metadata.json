{"groups": [{"name": "cache-config", "type": "com.stadium.config.properties.CacheConfigProperties", "sourceType": "com.stadium.config.properties.CacheConfigProperties"}, {"name": "encryption", "type": "com.stadium.config.EncryptionConfig", "sourceType": "com.stadium.config.EncryptionConfig"}, {"name": "encryption", "type": "com.stadium.config.properties.EncryptionProperties", "sourceType": "com.stadium.config.properties.EncryptionProperties"}, {"name": "export", "type": "com.stadium.config.ExportConfig", "sourceType": "com.stadium.config.ExportConfig"}, {"name": "export", "type": "com.stadium.config.ExportProperties", "sourceType": "com.stadium.config.ExportProperties"}, {"name": "performance", "type": "com.stadium.config.properties.PerformanceProperties", "sourceType": "com.stadium.config.properties.PerformanceProperties"}, {"name": "performance.monitor", "type": "com.stadium.config.properties.PerformanceProperties$Monitor", "sourceType": "com.stadium.config.properties.PerformanceProperties", "sourceMethod": "getMonitor()"}, {"name": "rate-limit", "type": "com.stadium.config.RateLimitProperties", "sourceType": "com.stadium.config.RateLimitProperties"}, {"name": "security", "type": "com.stadium.config.SecurityProperties", "sourceType": "com.stadium.config.SecurityProperties"}, {"name": "security.jwt", "type": "com.stadium.config.SecurityProperties$Jwt", "sourceType": "com.stadium.config.SecurityProperties", "sourceMethod": "getJwt()"}, {"name": "sms", "type": "com.stadium.config.SmsConfig", "sourceType": "com.stadium.config.SmsConfig"}, {"name": "sms.aliyun", "type": "com.stadium.config.SmsConfig$AliyunConfig", "sourceType": "com.stadium.config.SmsConfig", "sourceMethod": "get<PERSON><PERSON>yun()"}, {"name": "sms.tencent", "type": "com.stadium.config.SmsConfig$TencentConfig", "sourceType": "com.stadium.config.SmsConfig", "sourceMethod": "getTencent()"}, {"name": "springdoc", "type": "com.stadium.config.SpringDocProperties", "sourceType": "com.stadium.config.SpringDocProperties"}, {"name": "springdoc", "type": "com.stadium.config.SwaggerProperties", "sourceType": "com.stadium.config.SwaggerProperties"}, {"name": "springdoc.api-docs", "type": "com.stadium.config.SpringDocProperties$ApiDocs", "sourceType": "com.stadium.config.SpringDocProperties", "sourceMethod": "getApiDocs()"}, {"name": "springdoc.api-docs", "type": "com.stadium.config.SwaggerProperties$ApiDocs", "sourceType": "com.stadium.config.SwaggerProperties", "sourceMethod": "getApiDocs()"}, {"name": "springdoc.api-docs.groups", "type": "com.stadium.config.SpringDocProperties$ApiDocs$Groups", "sourceType": "com.stadium.config.SpringDocProperties$ApiDocs", "sourceMethod": "getGroups()"}, {"name": "springdoc.swagger-ui", "type": "com.stadium.config.SpringDocProperties$SwaggerUi", "sourceType": "com.stadium.config.SpringDocProperties", "sourceMethod": "getSwaggerUi()"}, {"name": "springdoc.swagger-ui", "type": "com.stadium.config.SwaggerProperties$SwaggerUi", "sourceType": "com.stadium.config.SwaggerProperties", "sourceMethod": "getSwaggerUi()"}, {"name": "springdoc.swagger-ui.contact", "type": "com.stadium.config.SwaggerProperties$SwaggerUi$Contact", "sourceType": "com.stadium.config.SwaggerProperties$SwaggerUi", "sourceMethod": "getContact()"}, {"name": "stadium", "type": "com.stadium.config.properties.StadiumProperties", "sourceType": "com.stadium.config.properties.StadiumProperties"}, {"name": "stadium.backup", "type": "com.stadium.config.BackupProperties", "sourceType": "com.stadium.config.BackupProperties"}, {"name": "stadium.cors", "type": "com.stadium.config.CorsProperties", "sourceType": "com.stadium.config.CorsProperties"}, {"name": "stadium.cors", "type": "com.stadium.config.properties.StadiumProperties$Cors", "sourceType": "com.stadium.config.properties.StadiumProperties", "sourceMethod": "getCors()"}, {"name": "stadium.jwt", "type": "com.stadium.config.JwtConfig", "sourceType": "com.stadium.config.JwtConfig"}, {"name": "stadium.jwt", "type": "com.stadium.config.JwtProperties", "sourceType": "com.stadium.config.JwtProperties"}, {"name": "stadium.jwt", "type": "com.stadium.config.properties.StadiumProperties$Jwt", "sourceType": "com.stadium.config.properties.StadiumProperties", "sourceMethod": "getJwt()"}, {"name": "stadium.payment.internal", "type": "com.stadium.config.InternalPaymentConfig", "sourceType": "com.stadium.config.InternalPaymentConfig"}, {"name": "stadium.swagger", "type": "com.stadium.config.StadiumSwaggerProperties", "sourceType": "com.stadium.config.StadiumSwaggerProperties"}, {"name": "stadium.swagger", "type": "com.stadium.config.properties.StadiumProperties$Swagger", "sourceType": "com.stadium.config.properties.StadiumProperties", "sourceMethod": "getSwagger()"}, {"name": "stadium.swagger.contact", "type": "com.stadium.config.StadiumSwaggerProperties$Contact", "sourceType": "com.stadium.config.StadiumSwaggerProperties", "sourceMethod": "getContact()"}, {"name": "stadium.swagger.license", "type": "com.stadium.config.StadiumSwaggerProperties$License", "sourceType": "com.stadium.config.StadiumSwaggerProperties", "sourceMethod": "getLicense()"}, {"name": "stadium.upload", "type": "com.stadium.config.StadiumUploadProperties", "sourceType": "com.stadium.config.StadiumUploadProperties"}, {"name": "stadium.upload", "type": "com.stadium.config.properties.StadiumProperties$Upload", "sourceType": "com.stadium.config.properties.StadiumProperties", "sourceMethod": "getUpload()"}, {"name": "stadium.venue.payment.methods", "type": "com.stadium.config.PaymentMethodConfig", "sourceType": "com.stadium.config.PaymentMethodConfig"}, {"name": "task", "type": "com.stadium.config.properties.TaskProperties", "sourceType": "com.stadium.config.properties.TaskProperties"}, {"name": "task.execution", "type": "com.stadium.config.TaskExecutionProperties", "sourceType": "com.stadium.config.TaskExecutionProperties"}, {"name": "task.execution", "type": "com.stadium.config.properties.TaskProperties$Execution", "sourceType": "com.stadium.config.properties.TaskProperties", "sourceMethod": "getExecution()"}, {"name": "task.execution.pool", "type": "com.stadium.config.TaskExecutionProperties$Pool", "sourceType": "com.stadium.config.TaskExecutionProperties", "sourceMethod": "getPool()"}, {"name": "task.execution.pool", "type": "com.stadium.config.properties.TaskProperties$Execution$Pool", "sourceType": "com.stadium.config.properties.TaskProperties$Execution", "sourceMethod": "getPool()"}, {"name": "upload", "type": "com.stadium.config.FileUploadConfig", "sourceType": "com.stadium.config.FileUploadConfig"}, {"name": "upload", "type": "com.stadium.config.UploadConfig", "sourceType": "com.stadium.config.UploadConfig"}, {"name": "upload", "type": "com.stadium.config.UploadProperties", "sourceType": "com.stadium.config.UploadProperties"}], "properties": [{"name": "cache-config.local-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用本地缓存", "sourceType": "com.stadium.config.properties.CacheConfigProperties", "defaultValue": true}, {"name": "cache-config.redis-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用Redis缓存", "sourceType": "com.stadium.config.properties.CacheConfigProperties", "defaultValue": true}, {"name": "encryption.secret-key", "type": "java.lang.String", "description": "密钥", "sourceType": "com.stadium.config.EncryptionConfig", "defaultValue": "U3RhZGl1bUVuY3J5cHRpb25LZXkyMDI0"}, {"name": "encryption.secret-key", "type": "java.lang.String", "description": "加密密钥", "sourceType": "com.stadium.config.properties.EncryptionProperties", "defaultValue": "stadium-management-encryption-secret-key-2024"}, {"name": "export.base-dir", "type": "java.lang.String", "sourceType": "com.stadium.config.ExportConfig"}, {"name": "export.base-dir", "type": "java.lang.String", "sourceType": "com.stadium.config.ExportProperties", "defaultValue": "./exports"}, {"name": "performance.monitor.cache-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用缓存监控", "sourceType": "com.stadium.config.properties.PerformanceProperties$Monitor", "defaultValue": true}, {"name": "performance.monitor.db-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用数据库监控", "sourceType": "com.stadium.config.properties.PerformanceProperties$Monitor", "defaultValue": true}, {"name": "performance.monitor.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用性能监控", "sourceType": "com.stadium.config.properties.PerformanceProperties$Monitor", "defaultValue": true}, {"name": "performance.monitor.jvm-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用JVM监控", "sourceType": "com.stadium.config.properties.PerformanceProperties$Monitor", "defaultValue": true}, {"name": "performance.monitor.method-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用方法监控", "sourceType": "com.stadium.config.properties.PerformanceProperties$Monitor", "defaultValue": true}, {"name": "rate-limit.capacity", "type": "java.lang.Integer", "sourceType": "com.stadium.config.RateLimitProperties", "defaultValue": 100}, {"name": "rate-limit.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.stadium.config.RateLimitProperties", "defaultValue": true}, {"name": "rate-limit.refill-period", "type": "java.lang.Integer", "sourceType": "com.stadium.config.RateLimitProperties", "defaultValue": 1}, {"name": "rate-limit.refill-rate", "type": "java.lang.Integer", "sourceType": "com.stadium.config.RateLimitProperties", "defaultValue": 1}, {"name": "rate-limit.refill-time-unit", "type": "java.lang.String", "sourceType": "com.stadium.config.RateLimitProperties", "defaultValue": "SECONDS"}, {"name": "security.ignored-urls", "type": "java.lang.String[]", "sourceType": "com.stadium.config.SecurityProperties", "defaultValue": ["/swagger-ui/**", "/v3/api-docs/**", "/actuator/**", "/druid/**", "/auth/login", "/auth/register"]}, {"name": "security.jwt.base64-secret", "type": "java.lang.String", "sourceType": "com.stadium.config.SecurityProperties$Jwt"}, {"name": "security.jwt.header", "type": "java.lang.String", "sourceType": "com.stadium.config.SecurityProperties$Jwt", "defaultValue": "Authorization"}, {"name": "security.jwt.token-start-with", "type": "java.lang.String", "sourceType": "com.stadium.config.SecurityProperties$Jwt", "defaultValue": "Bearer"}, {"name": "security.jwt.token-validity-in-seconds", "type": "java.lang.Long", "sourceType": "com.stadium.config.SecurityProperties$Jwt", "defaultValue": 86400}, {"name": "sms.aliyun.access-key-id", "type": "java.lang.String", "description": "访问密钥ID", "sourceType": "com.stadium.config.SmsConfig$AliyunConfig"}, {"name": "sms.aliyun.access-key-secret", "type": "java.lang.String", "description": "访问密钥密码", "sourceType": "com.stadium.config.SmsConfig$AliyunConfig"}, {"name": "sms.aliyun.sign-name", "type": "java.lang.String", "description": "短信签名", "sourceType": "com.stadium.config.SmsConfig$AliyunConfig"}, {"name": "sms.aliyun.template-code", "type": "java.lang.String", "description": "短信模板代码", "sourceType": "com.stadium.config.SmsConfig$AliyunConfig"}, {"name": "sms.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用短信服务（系统内部化，禁用外部短信服务）", "sourceType": "com.stadium.config.SmsConfig", "defaultValue": false}, {"name": "sms.mock", "type": "java.lang.Bo<PERSON>an", "description": "是否使用模拟短信服务（系统内部化，仅支持站内消息）", "sourceType": "com.stadium.config.SmsConfig", "defaultValue": true}, {"name": "sms.provider", "type": "java.lang.String", "description": "短信服务提供商，可选值：mock, aliyun, tencent", "sourceType": "com.stadium.config.SmsConfig", "defaultValue": "tencent"}, {"name": "sms.tencent.app-id", "type": "java.lang.String", "description": "应用ID", "sourceType": "com.stadium.config.SmsConfig$TencentConfig"}, {"name": "sms.tencent.app-key", "type": "java.lang.String", "description": "应用密钥", "sourceType": "com.stadium.config.SmsConfig$TencentConfig"}, {"name": "sms.tencent.sign-name", "type": "java.lang.String", "description": "短信签名", "sourceType": "com.stadium.config.SmsConfig$TencentConfig"}, {"name": "sms.tencent.template-id", "type": "java.lang.String", "description": "短信模板ID", "sourceType": "com.stadium.config.SmsConfig$TencentConfig"}, {"name": "springdoc.api-docs.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用API文档", "sourceType": "com.stadium.config.SpringDocProperties$ApiDocs", "defaultValue": true}, {"name": "springdoc.api-docs.groups.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用分组", "sourceType": "com.stadium.config.SpringDocProperties$ApiDocs$Groups", "defaultValue": true}, {"name": "springdoc.api-docs.path", "type": "java.lang.String", "description": "API文档路径", "sourceType": "com.stadium.config.SpringDocProperties$ApiDocs", "defaultValue": "/v3/api-docs"}, {"name": "springdoc.api-docs.path", "type": "java.lang.String", "sourceType": "com.stadium.config.SwaggerProperties$ApiDocs", "defaultValue": "/v3/api-docs"}, {"name": "springdoc.group-configs", "type": "java.util.List<com.stadium.config.SwaggerProperties$GroupConfig>", "sourceType": "com.stadium.config.SwaggerProperties"}, {"name": "springdoc.swagger-ui.config-url", "type": "java.lang.String", "description": "配置URL", "sourceType": "com.stadium.config.SpringDocProperties$SwaggerUi", "defaultValue": "/v3/api-docs/swagger-config"}, {"name": "springdoc.swagger-ui.contact.email", "type": "java.lang.String", "sourceType": "com.stadium.config.SwaggerProperties$SwaggerUi$Contact"}, {"name": "springdoc.swagger-ui.contact.name", "type": "java.lang.String", "sourceType": "com.stadium.config.SwaggerProperties$SwaggerUi$Contact"}, {"name": "springdoc.swagger-ui.description", "type": "java.lang.String", "sourceType": "com.stadium.config.SwaggerProperties$SwaggerUi", "defaultValue": "提供场馆管理系统的所有接口文档"}, {"name": "springdoc.swagger-ui.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用Swagger UI", "sourceType": "com.stadium.config.SpringDocProperties$SwaggerUi", "defaultValue": true}, {"name": "springdoc.swagger-ui.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.stadium.config.SwaggerProperties$SwaggerUi", "defaultValue": true}, {"name": "springdoc.swagger-ui.operations-sorter", "type": "java.lang.String", "description": "操作排序", "sourceType": "com.stadium.config.SpringDocProperties$SwaggerUi", "defaultValue": "alpha"}, {"name": "springdoc.swagger-ui.operations-sorter", "type": "java.lang.String", "sourceType": "com.stadium.config.SwaggerProperties$SwaggerUi", "defaultValue": "alpha"}, {"name": "springdoc.swagger-ui.path", "type": "java.lang.String", "description": "Swagger UI路径", "sourceType": "com.stadium.config.SpringDocProperties$SwaggerUi", "defaultValue": "/swagger-ui.html"}, {"name": "springdoc.swagger-ui.path", "type": "java.lang.String", "sourceType": "com.stadium.config.SwaggerProperties$SwaggerUi", "defaultValue": "/swagger-ui.html"}, {"name": "springdoc.swagger-ui.show-common-extensions", "type": "java.lang.Bo<PERSON>an", "description": "是否显示通用扩展", "sourceType": "com.stadium.config.SpringDocProperties$SwaggerUi", "defaultValue": true}, {"name": "springdoc.swagger-ui.show-extensions", "type": "java.lang.Bo<PERSON>an", "description": "是否显示扩展", "sourceType": "com.stadium.config.SpringDocProperties$SwaggerUi", "defaultValue": true}, {"name": "springdoc.swagger-ui.tags-sorter", "type": "java.lang.String", "description": "标签排序", "sourceType": "com.stadium.config.SpringDocProperties$SwaggerUi", "defaultValue": "alpha"}, {"name": "springdoc.swagger-ui.tags-sorter", "type": "java.lang.String", "sourceType": "com.stadium.config.SwaggerProperties$SwaggerUi", "defaultValue": "alpha"}, {"name": "springdoc.swagger-ui.title", "type": "java.lang.String", "sourceType": "com.stadium.config.SwaggerProperties$SwaggerUi", "defaultValue": "体育中心场馆智慧管理系统接口文档"}, {"name": "springdoc.swagger-ui.version", "type": "java.lang.String", "sourceType": "com.stadium.config.SwaggerProperties$SwaggerUi", "defaultValue": "1.0.0"}, {"name": "stadium.backup.backup-dir", "type": "java.lang.String", "description": "备份文件存储目录", "sourceType": "com.stadium.config.BackupProperties", "defaultValue": "backend/exports"}, {"name": "stadium.backup.db-host", "type": "java.lang.String", "description": "数据库主机", "sourceType": "com.stadium.config.BackupProperties", "defaultValue": "localhost"}, {"name": "stadium.backup.db-name", "type": "java.lang.String", "description": "数据库名称", "sourceType": "com.stadium.config.BackupProperties", "defaultValue": "stadium_management"}, {"name": "stadium.backup.db-password", "type": "java.lang.String", "description": "数据库密码", "sourceType": "com.stadium.config.BackupProperties", "defaultValue": ""}, {"name": "stadium.backup.db-port", "type": "java.lang.String", "description": "数据库端口", "sourceType": "com.stadium.config.BackupProperties", "defaultValue": "3306"}, {"name": "stadium.backup.db-username", "type": "java.lang.String", "description": "数据库用户名", "sourceType": "com.stadium.config.BackupProperties", "defaultValue": "root"}, {"name": "stadium.backup.mysql-dump-path", "type": "java.lang.String", "description": "MySQL备份命令路径", "sourceType": "com.stadium.config.BackupProperties", "defaultValue": "mysqldump"}, {"name": "stadium.backup.mysql-path", "type": "java.lang.String", "description": "MySQL命令路径", "sourceType": "com.stadium.config.BackupProperties", "defaultValue": "mysql"}, {"name": "stadium.cors.allow-credentials", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.stadium.config.CorsProperties", "defaultValue": true}, {"name": "stadium.cors.allow-credentials", "type": "java.lang.Bo<PERSON>an", "description": "是否允许凭证", "sourceType": "com.stadium.config.properties.StadiumProperties$Cors", "defaultValue": true}, {"name": "stadium.cors.allowed-headers", "type": "java.lang.String[]", "sourceType": "com.stadium.config.CorsProperties", "defaultValue": ["*"]}, {"name": "stadium.cors.allowed-headers", "type": "java.lang.String", "description": "允许的头部", "sourceType": "com.stadium.config.properties.StadiumProperties$Cors", "defaultValue": "*"}, {"name": "stadium.cors.allowed-methods", "type": "java.lang.String[]", "sourceType": "com.stadium.config.CorsProperties", "defaultValue": ["GET", "POST", "PUT", "DELETE", "OPTIONS"]}, {"name": "stadium.cors.allowed-methods", "type": "java.lang.String", "description": "允许的方法", "sourceType": "com.stadium.config.properties.StadiumProperties$Cors", "defaultValue": "GET,POST,PUT,DELETE,OPTIONS"}, {"name": "stadium.cors.allowed-origins", "type": "java.lang.String[]", "sourceType": "com.stadium.config.CorsProperties", "defaultValue": ["*"]}, {"name": "stadium.cors.allowed-origins", "type": "java.lang.String", "description": "允许的来源", "sourceType": "com.stadium.config.properties.StadiumProperties$Cors", "defaultValue": "http://localhost:8081,http://localhost:3000"}, {"name": "stadium.cors.exposed-headers", "type": "java.lang.String[]", "sourceType": "com.stadium.config.CorsProperties", "defaultValue": ["Authorization", "Content-Type"]}, {"name": "stadium.cors.exposed-headers", "type": "java.lang.String", "description": "暴露的头部", "sourceType": "com.stadium.config.properties.StadiumProperties$Cors", "defaultValue": "Authorization,Content-Type"}, {"name": "stadium.cors.max-age", "type": "java.lang.Long", "sourceType": "com.stadium.config.CorsProperties", "defaultValue": 3600}, {"name": "stadium.cors.max-age", "type": "java.lang.Long", "description": "预检请求缓存时间", "sourceType": "com.stadium.config.properties.StadiumProperties$Cors", "defaultValue": 3600}, {"name": "stadium.jwt.access-token-expiration", "type": "java.lang.Long", "description": "访问令牌过期时间（秒）", "sourceType": "com.stadium.config.JwtConfig", "defaultValue": 3600}, {"name": "stadium.jwt.access-token-expiration", "type": "java.lang.Long", "description": "JWT访问令牌过期时间（秒）", "sourceType": "com.stadium.config.JwtProperties"}, {"name": "stadium.jwt.access-token-expiration", "type": "java.lang.Long", "description": "访问令牌过期时间（秒）", "sourceType": "com.stadium.config.properties.StadiumProperties$Jwt", "defaultValue": 3600}, {"name": "stadium.jwt.refresh-token-expiration", "type": "java.lang.Long", "description": "刷新令牌过期时间（秒）", "sourceType": "com.stadium.config.JwtConfig", "defaultValue": 604800}, {"name": "stadium.jwt.refresh-token-expiration", "type": "java.lang.Long", "description": "JWT刷新令牌过期时间（秒）", "sourceType": "com.stadium.config.JwtProperties"}, {"name": "stadium.jwt.refresh-token-expiration", "type": "java.lang.Long", "description": "刷新令牌过期时间（秒）", "sourceType": "com.stadium.config.properties.StadiumProperties$Jwt", "defaultValue": 604800}, {"name": "stadium.jwt.secret", "type": "java.lang.String", "description": "密钥", "sourceType": "com.stadium.config.JwtConfig", "defaultValue": "stadium-management-system-secret-key"}, {"name": "stadium.jwt.secret", "type": "java.lang.String", "description": "JWT密钥", "sourceType": "com.stadium.config.JwtProperties"}, {"name": "stadium.jwt.secret", "type": "java.lang.String", "description": "JWT密钥", "sourceType": "com.stadium.config.properties.StadiumProperties$Jwt", "defaultValue": "stadium-management-system-secret-key"}, {"name": "stadium.payment.internal.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用站内支付", "sourceType": "com.stadium.config.InternalPaymentConfig", "defaultValue": true}, {"name": "stadium.payment.internal.fee-rate", "type": "java.math.BigDecimal", "description": "手续费率", "sourceType": "com.stadium.config.InternalPaymentConfig"}, {"name": "stadium.payment.internal.min-balance", "type": "java.math.BigDecimal", "description": "最低余额要求", "sourceType": "com.stadium.config.InternalPaymentConfig"}, {"name": "stadium.swagger.contact.email", "type": "java.lang.String", "description": "联系人邮箱", "sourceType": "com.stadium.config.StadiumSwaggerProperties$Contact", "defaultValue": "<EMAIL>"}, {"name": "stadium.swagger.contact.name", "type": "java.lang.String", "description": "联系人姓名", "sourceType": "com.stadium.config.StadiumSwaggerProperties$Contact", "defaultValue": "开发团队"}, {"name": "stadium.swagger.contact.url", "type": "java.lang.String", "description": "联系人网址", "sourceType": "com.stadium.config.StadiumSwaggerProperties$Contact", "defaultValue": "https://stadium.com"}, {"name": "stadium.swagger.description", "type": "java.lang.String", "description": "API描述", "sourceType": "com.stadium.config.StadiumSwaggerProperties", "defaultValue": "体育场馆管理系统的RESTful API文档"}, {"name": "stadium.swagger.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用Swagger", "sourceType": "com.stadium.config.StadiumSwaggerProperties", "defaultValue": true}, {"name": "stadium.swagger.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用Swagger", "sourceType": "com.stadium.config.properties.StadiumProperties$Swagger", "defaultValue": true}, {"name": "stadium.swagger.license.name", "type": "java.lang.String", "description": "许可证名称", "sourceType": "com.stadium.config.StadiumSwaggerProperties$License", "defaultValue": "Apache 2.0"}, {"name": "stadium.swagger.license.url", "type": "java.lang.String", "description": "许可证网址", "sourceType": "com.stadium.config.StadiumSwaggerProperties$License", "defaultValue": "https://www.apache.org/licenses/LICENSE-2.0"}, {"name": "stadium.swagger.title", "type": "java.lang.String", "description": "API标题", "sourceType": "com.stadium.config.StadiumSwaggerProperties", "defaultValue": "体育场馆管理系统API"}, {"name": "stadium.swagger.version", "type": "java.lang.String", "description": "API版本", "sourceType": "com.stadium.config.StadiumSwaggerProperties", "defaultValue": "1.0.0"}, {"name": "stadium.upload.allowed-types", "type": "java.lang.String", "description": "允许的文件类型", "sourceType": "com.stadium.config.StadiumUploadProperties", "defaultValue": "jpg,jpeg,png,gif"}, {"name": "stadium.upload.allowed-types", "type": "java.lang.String", "description": "允许的文件类型", "sourceType": "com.stadium.config.properties.StadiumProperties$Upload", "defaultValue": "jpg,jpeg,png,gif"}, {"name": "stadium.upload.max-size", "type": "java.lang.Long", "description": "最大文件大小（字节）", "sourceType": "com.stadium.config.StadiumUploadProperties", "defaultValue": 10485760}, {"name": "stadium.upload.max-size", "type": "java.lang.Long", "description": "最大文件大小", "sourceType": "com.stadium.config.properties.StadiumProperties$Upload", "defaultValue": 10485760}, {"name": "stadium.upload.path", "type": "java.lang.String", "description": "上传文件保存路径", "sourceType": "com.stadium.config.StadiumUploadProperties", "defaultValue": "uploads/"}, {"name": "stadium.upload.path", "type": "java.lang.String", "description": "上传路径", "sourceType": "com.stadium.config.properties.StadiumProperties$Upload", "defaultValue": "uploads/"}, {"name": "stadium.venue.payment.methods.balance", "type": "java.lang.Integer", "description": "余额支付", "sourceType": "com.stadium.config.PaymentMethodConfig", "defaultValue": 1}, {"name": "stadium.venue.payment.methods.points", "type": "java.lang.Integer", "description": "积分支付", "sourceType": "com.stadium.config.PaymentMethodConfig", "defaultValue": 2}, {"name": "task.execution.pool.allow-core-thread-timeout", "type": "java.lang.Bo<PERSON>an", "description": "是否允许核心线程超时", "sourceType": "com.stadium.config.TaskExecutionProperties$Pool", "defaultValue": true}, {"name": "task.execution.pool.allow-core-thread-timeout", "type": "java.lang.Bo<PERSON>an", "description": "是否允许核心线程超时", "sourceType": "com.stadium.config.properties.TaskProperties$Execution$Pool", "defaultValue": true}, {"name": "task.execution.pool.core-size", "type": "java.lang.Integer", "description": "核心线程数", "sourceType": "com.stadium.config.TaskExecutionProperties$Pool", "defaultValue": 8}, {"name": "task.execution.pool.core-size", "type": "java.lang.Integer", "description": "核心线程数", "sourceType": "com.stadium.config.properties.TaskProperties$Execution$Pool", "defaultValue": 8}, {"name": "task.execution.pool.keep-alive", "type": "java.lang.Integer", "description": "线程空闲超时时间（秒）", "sourceType": "com.stadium.config.TaskExecutionProperties$Pool", "defaultValue": 60}, {"name": "task.execution.pool.keep-alive", "type": "java.lang.Integer", "description": "线程保活时间（秒）", "sourceType": "com.stadium.config.properties.TaskProperties$Execution$Pool", "defaultValue": 60}, {"name": "task.execution.pool.max-size", "type": "java.lang.Integer", "description": "最大线程数", "sourceType": "com.stadium.config.TaskExecutionProperties$Pool", "defaultValue": 16}, {"name": "task.execution.pool.max-size", "type": "java.lang.Integer", "description": "最大线程数", "sourceType": "com.stadium.config.properties.TaskProperties$Execution$Pool", "defaultValue": 16}, {"name": "task.execution.pool.queue-capacity", "type": "java.lang.Integer", "description": "队列容量", "sourceType": "com.stadium.config.TaskExecutionProperties$Pool", "defaultValue": 100}, {"name": "task.execution.pool.queue-capacity", "type": "java.lang.Integer", "description": "队列容量", "sourceType": "com.stadium.config.properties.TaskProperties$Execution$Pool", "defaultValue": 100}, {"name": "task.execution.pool.thread-name-prefix", "type": "java.lang.String", "description": "线程名称前缀", "sourceType": "com.stadium.config.TaskExecutionProperties$Pool", "defaultValue": "task-"}, {"name": "task.execution.pool.thread-name-prefix", "type": "java.lang.String", "description": "线程名前缀", "sourceType": "com.stadium.config.properties.TaskProperties$Execution$Pool", "defaultValue": "stadium-task-"}, {"name": "upload.allowed-types", "type": "java.lang.String", "description": "允许的文件类型，多个用逗号分隔", "sourceType": "com.stadium.config.FileUploadConfig", "defaultValue": "jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx"}, {"name": "upload.allowed-types", "type": "java.lang.String", "description": "允许的文件类型", "sourceType": "com.stadium.config.UploadConfig", "defaultValue": "jpg,jpeg,png,gif"}, {"name": "upload.allowed-types", "type": "java.lang.String", "description": "允许的文件类型，多个用逗号分隔", "sourceType": "com.stadium.config.UploadProperties", "defaultValue": "jpg,jpeg,png,gif"}, {"name": "upload.avatar-path", "type": "java.lang.String", "description": "头像保存路径", "sourceType": "com.stadium.config.FileUploadConfig", "defaultValue": "avatars/"}, {"name": "upload.avatar-path", "type": "java.lang.String", "description": "头像上传路径", "sourceType": "com.stadium.config.UploadConfig", "defaultValue": "uploads/avatars/"}, {"name": "upload.avatar-path", "type": "java.lang.String", "sourceType": "com.stadium.config.UploadProperties", "defaultValue": "uploads/avatars/"}, {"name": "upload.image-path", "type": "java.lang.String", "description": "图片保存路径", "sourceType": "com.stadium.config.FileUploadConfig", "defaultValue": "images/"}, {"name": "upload.image-path", "type": "java.lang.String", "description": "图片上传路径", "sourceType": "com.stadium.config.UploadConfig", "defaultValue": "uploads/images/"}, {"name": "upload.image-path", "type": "java.lang.String", "sourceType": "com.stadium.config.UploadProperties", "defaultValue": "uploads/images/"}, {"name": "upload.max-size", "type": "java.lang.String", "description": "最大文件大小", "sourceType": "com.stadium.config.FileUploadConfig", "defaultValue": "10MB"}, {"name": "upload.max-size", "type": "java.lang.Long", "description": "最大文件大小（字节）", "sourceType": "com.stadium.config.UploadConfig", "defaultValue": 0}, {"name": "upload.max-size", "type": "java.lang.Long", "description": "最大文件大小", "sourceType": "com.stadium.config.UploadProperties", "defaultValue": 10485760}, {"name": "upload.path", "type": "java.lang.String", "description": "上传文件保存路径", "sourceType": "com.stadium.config.FileUploadConfig", "defaultValue": "uploads/"}, {"name": "upload.path", "type": "java.lang.String", "description": "上传路径", "sourceType": "com.stadium.config.UploadConfig", "defaultValue": "uploads/"}, {"name": "upload.path", "type": "java.lang.String", "description": "上传文件保存路径", "sourceType": "com.stadium.config.UploadProperties", "defaultValue": "uploads/"}], "hints": []}