{"groups": [{"name": "cache-config", "type": "com.stadium.config.CacheConfigProperties", "sourceType": "com.stadium.config.CacheConfigProperties"}, {"name": "encryption", "type": "com.stadium.config.EncryptionConfig", "sourceType": "com.stadium.config.EncryptionConfig"}, {"name": "export", "type": "com.stadium.config.ExportConfig", "sourceType": "com.stadium.config.ExportConfig"}, {"name": "performance", "type": "com.stadium.config.PerformanceProperties", "sourceType": "com.stadium.config.PerformanceProperties"}, {"name": "performance.monitor", "type": "com.stadium.config.PerformanceProperties$Monitor", "sourceType": "com.stadium.config.PerformanceProperties", "sourceMethod": "public com.stadium.config.PerformanceProperties.Monitor getMonitor() "}, {"name": "rate-limit", "type": "com.stadium.config.RateLimitProperties", "sourceType": "com.stadium.config.RateLimitProperties"}, {"name": "security", "type": "com.stadium.config.SecurityProperties", "sourceType": "com.stadium.config.SecurityProperties"}, {"name": "security.jwt", "type": "com.stadium.config.SecurityProperties$Jwt", "sourceType": "com.stadium.config.SecurityProperties", "sourceMethod": "public com.stadium.config.SecurityProperties.Jwt getJwt() "}, {"name": "sms", "type": "com.stadium.config.SmsConfig", "sourceType": "com.stadium.config.SmsConfig"}, {"name": "sms.aliyun", "type": "com.stadium.config.SmsConfig$AliyunConfig", "sourceType": "com.stadium.config.SmsConfig", "sourceMethod": "public com.stadium.config.SmsConfig.AliyunConfig getAliyun() "}, {"name": "sms.tencent", "type": "com.stadium.config.SmsConfig$TencentConfig", "sourceType": "com.stadium.config.SmsConfig", "sourceMethod": "public com.stadium.config.SmsConfig.TencentConfig getTencent() "}, {"name": "springdoc", "type": "com.stadium.config.SpringDocProperties", "sourceType": "com.stadium.config.SpringDocProperties"}, {"name": "springdoc.api-docs", "type": "com.stadium.config.SpringDocProperties$ApiDocs", "sourceType": "com.stadium.config.SpringDocProperties", "sourceMethod": "public com.stadium.config.SpringDocProperties.ApiDocs getApiDocs() "}, {"name": "springdoc.api-docs.groups", "type": "com.stadium.config.SpringDocProperties$ApiDocs$Groups", "sourceType": "com.stadium.config.SpringDocProperties$ApiDocs", "sourceMethod": "public com.stadium.config.SpringDocProperties.ApiDocs.Groups getGroups() "}, {"name": "springdoc.swagger-ui", "type": "com.stadium.config.SpringDocProperties$SwaggerUi", "sourceType": "com.stadium.config.SpringDocProperties", "sourceMethod": "public com.stadium.config.SpringDocProperties.SwaggerUi getSwaggerUi() "}, {"name": "springdoc.swagger-ui.contact", "type": "com.stadium.config.SwaggerProperties$SwaggerUi$Contact", "sourceType": "com.stadium.config.SwaggerProperties$SwaggerUi", "sourceMethod": "public com.stadium.config.SwaggerProperties.SwaggerUi.Contact getContact() "}, {"name": "stadium", "type": "com.stadium.config.StadiumProperties", "sourceType": "com.stadium.config.StadiumProperties"}, {"name": "stadium.backup", "type": "com.stadium.config.BackupProperties", "sourceType": "com.stadium.config.BackupProperties"}, {"name": "stadium.check-in", "type": "com.stadium.config.StadiumProperties$CheckIn", "sourceType": "com.stadium.config.StadiumProperties", "sourceMethod": "public com.stadium.config.StadiumProperties.CheckIn getCheckIn() "}, {"name": "stadium.check-in.statistics", "type": "com.stadium.config.StadiumProperties$CheckIn$Statistics", "sourceType": "com.stadium.config.StadiumProperties$CheckIn", "sourceMethod": "public com.stadium.config.StadiumProperties.CheckIn.Statistics getStatistics() "}, {"name": "stadium.check-in.statistics.cache", "type": "com.stadium.config.StadiumProperties$CheckIn$Statistics$Cache", "sourceType": "com.stadium.config.StadiumProperties$CheckIn$Statistics", "sourceMethod": "public com.stadium.config.StadiumProperties.CheckIn.Statistics.Cache getCache() "}, {"name": "stadium.check-in.statistics.export", "type": "com.stadium.config.StadiumProperties$CheckIn$Statistics$Export", "sourceType": "com.stadium.config.StadiumProperties$CheckIn$Statistics", "sourceMethod": "public com.stadium.config.StadiumProperties.CheckIn.Statistics.Export getExport() "}, {"name": "stadium.check-in.statistics.task", "type": "com.stadium.config.StadiumProperties$CheckIn$Statistics$Task", "sourceType": "com.stadium.config.StadiumProperties$CheckIn$Statistics", "sourceMethod": "public com.stadium.config.StadiumProperties.CheckIn.Statistics.Task getTask() "}, {"name": "stadium.cors", "type": "com.stadium.config.CorsProperties", "sourceType": "com.stadium.config.CorsProperties"}, {"name": "stadium.jwt", "type": "com.stadium.config.JwtConfig", "sourceType": "com.stadium.config.JwtConfig"}, {"name": "stadium.payment.internal", "type": "com.stadium.config.InternalPaymentConfig", "sourceType": "com.stadium.config.InternalPaymentConfig"}, {"name": "stadium.swagger", "type": "com.stadium.config.StadiumSwaggerProperties", "sourceType": "com.stadium.config.StadiumSwaggerProperties"}, {"name": "stadium.swagger.contact", "type": "com.stadium.config.StadiumSwaggerProperties$Contact", "sourceType": "com.stadium.config.StadiumSwaggerProperties", "sourceMethod": "public com.stadium.config.StadiumSwaggerProperties.Contact getContact() "}, {"name": "stadium.swagger.license", "type": "com.stadium.config.StadiumSwaggerProperties$License", "sourceType": "com.stadium.config.StadiumSwaggerProperties", "sourceMethod": "public com.stadium.config.StadiumSwaggerProperties.License getLicense() "}, {"name": "stadium.upload", "type": "com.stadium.config.StadiumUploadProperties", "sourceType": "com.stadium.config.StadiumUploadProperties"}, {"name": "stadium.venue", "type": "com.stadium.config.StadiumProperties$Venue", "sourceType": "com.stadium.config.StadiumProperties", "sourceMethod": "public com.stadium.config.StadiumProperties.Venue getVenue() "}, {"name": "stadium.venue.booking", "type": "com.stadium.config.StadiumProperties$Venue$Booking", "sourceType": "com.stadium.config.StadiumProperties$Venue", "sourceMethod": "public com.stadium.config.StadiumProperties.Venue.Booking getBooking() "}, {"name": "stadium.venue.booking.status", "type": "com.stadium.config.StadiumProperties$Venue$Booking$Status", "sourceType": "com.stadium.config.StadiumProperties$Venue$Booking", "sourceMethod": "public com.stadium.config.StadiumProperties.Venue.Booking.Status getStatus() "}, {"name": "stadium.venue.payment", "type": "com.stadium.config.StadiumProperties$Venue$Payment", "sourceType": "com.stadium.config.StadiumProperties$Venue", "sourceMethod": "public com.stadium.config.StadiumProperties.Venue.Payment getPayment() "}, {"name": "stadium.venue.payment.methods", "type": "com.stadium.config.PaymentMethodConfig", "sourceType": "com.stadium.config.PaymentMethodConfig"}, {"name": "stadium.venue.rating", "type": "com.stadium.config.StadiumProperties$Venue$Rating", "sourceType": "com.stadium.config.StadiumProperties$Venue", "sourceMethod": "public com.stadium.config.StadiumProperties.Venue.Rating getRating() "}, {"name": "stadium.venue.refund", "type": "com.stadium.config.StadiumProperties$Venue$Refund", "sourceType": "com.stadium.config.StadiumProperties$Venue", "sourceMethod": "public com.stadium.config.StadiumProperties.Venue.Refund getRefund() "}, {"name": "stadium.venue.statistics", "type": "com.stadium.config.StadiumProperties$Venue$Statistics", "sourceType": "com.stadium.config.StadiumProperties$Venue", "sourceMethod": "public com.stadium.config.StadiumProperties.Venue.Statistics getStatistics() "}, {"name": "stadium.venue.statistics.types", "type": "com.stadium.config.StadiumProperties$Venue$Statistics$Types", "sourceType": "com.stadium.config.StadiumProperties$Venue$Statistics", "sourceMethod": "public com.stadium.config.StadiumProperties.Venue.Statistics.Types getTypes() "}, {"name": "stadium.venue.status", "type": "com.stadium.config.StadiumProperties$Venue$Status", "sourceType": "com.stadium.config.StadiumProperties$Venue", "sourceMethod": "public com.stadium.config.StadiumProperties.Venue.Status getStatus() "}, {"name": "task.execution", "type": "com.stadium.config.TaskExecutionProperties", "sourceType": "com.stadium.config.TaskExecutionProperties"}, {"name": "task.execution.pool", "type": "com.stadium.config.TaskExecutionProperties$Pool", "sourceType": "com.stadium.config.TaskExecutionProperties", "sourceMethod": "public com.stadium.config.TaskExecutionProperties.Pool getPool() "}, {"name": "upload", "type": "com.stadium.config.FileUploadConfig", "sourceType": "com.stadium.config.FileUploadConfig"}], "properties": [{"name": "cache-config.local-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用本地缓存", "sourceType": "com.stadium.config.CacheConfigProperties"}, {"name": "cache-config.redis-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用Redis缓存", "sourceType": "com.stadium.config.CacheConfigProperties"}, {"name": "encryption.secret-key", "type": "java.lang.String", "description": "密钥", "sourceType": "com.stadium.config.EncryptionConfig"}, {"name": "export.base-dir", "type": "java.lang.String", "sourceType": "com.stadium.config.ExportConfig"}, {"name": "performance.monitor.cache-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用缓存性能监控", "sourceType": "com.stadium.config.PerformanceProperties$Monitor"}, {"name": "performance.monitor.db-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用数据库性能监控", "sourceType": "com.stadium.config.PerformanceProperties$Monitor"}, {"name": "performance.monitor.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用性能监控", "sourceType": "com.stadium.config.PerformanceProperties$Monitor"}, {"name": "performance.monitor.jvm-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用JVM性能监控", "sourceType": "com.stadium.config.PerformanceProperties$Monitor"}, {"name": "performance.monitor.method-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用方法性能监控", "sourceType": "com.stadium.config.PerformanceProperties$Monitor"}, {"name": "rate-limit.capacity", "type": "java.lang.Integer", "sourceType": "com.stadium.config.RateLimitProperties"}, {"name": "rate-limit.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.stadium.config.RateLimitProperties"}, {"name": "rate-limit.refill-period", "type": "java.lang.Integer", "sourceType": "com.stadium.config.RateLimitProperties"}, {"name": "rate-limit.refill-rate", "type": "java.lang.Integer", "sourceType": "com.stadium.config.RateLimitProperties"}, {"name": "rate-limit.refill-time-unit", "type": "java.lang.String", "sourceType": "com.stadium.config.RateLimitProperties"}, {"name": "security.ignored-urls", "type": "java.lang.String[]", "sourceType": "com.stadium.config.SecurityProperties"}, {"name": "security.jwt.base64-secret", "type": "java.lang.String", "sourceType": "com.stadium.config.SecurityProperties$Jwt"}, {"name": "security.jwt.header", "type": "java.lang.String", "sourceType": "com.stadium.config.SecurityProperties$Jwt"}, {"name": "security.jwt.token-start-with", "type": "java.lang.String", "sourceType": "com.stadium.config.SecurityProperties$Jwt"}, {"name": "security.jwt.token-validity-in-seconds", "type": "java.lang.Long", "sourceType": "com.stadium.config.SecurityProperties$Jwt"}, {"name": "sms.aliyun.access-key-id", "type": "java.lang.String", "description": "访问密钥ID", "sourceType": "com.stadium.config.SmsConfig$AliyunConfig"}, {"name": "sms.aliyun.access-key-secret", "type": "java.lang.String", "description": "访问密钥密码", "sourceType": "com.stadium.config.SmsConfig$AliyunConfig"}, {"name": "sms.aliyun.sign-name", "type": "java.lang.String", "description": "短信签名", "sourceType": "com.stadium.config.SmsConfig$AliyunConfig"}, {"name": "sms.aliyun.template-code", "type": "java.lang.String", "description": "短信模板代码", "sourceType": "com.stadium.config.SmsConfig$AliyunConfig"}, {"name": "sms.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用短信服务（系统内部化，禁用外部短信服务）", "sourceType": "com.stadium.config.SmsConfig"}, {"name": "sms.mock", "type": "java.lang.Bo<PERSON>an", "description": "是否使用模拟短信服务（系统内部化，仅支持站内消息）", "sourceType": "com.stadium.config.SmsConfig"}, {"name": "sms.provider", "type": "java.lang.String", "description": "短信服务提供商，可选值：mock, aliyun, tencent", "sourceType": "com.stadium.config.SmsConfig"}, {"name": "sms.tencent.app-id", "type": "java.lang.String", "description": "应用ID", "sourceType": "com.stadium.config.SmsConfig$TencentConfig"}, {"name": "sms.tencent.app-key", "type": "java.lang.String", "description": "应用密钥", "sourceType": "com.stadium.config.SmsConfig$TencentConfig"}, {"name": "sms.tencent.sign-name", "type": "java.lang.String", "description": "短信签名", "sourceType": "com.stadium.config.SmsConfig$TencentConfig"}, {"name": "sms.tencent.template-id", "type": "java.lang.String", "description": "短信模板ID", "sourceType": "com.stadium.config.SmsConfig$TencentConfig"}, {"name": "springdoc.api-docs.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用API文档", "sourceType": "com.stadium.config.SpringDocProperties$ApiDocs"}, {"name": "springdoc.api-docs.groups.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用分组", "sourceType": "com.stadium.config.SpringDocProperties$ApiDocs$Groups"}, {"name": "springdoc.api-docs.path", "type": "java.lang.String", "description": "API文档路径", "sourceType": "com.stadium.config.SpringDocProperties$ApiDocs"}, {"name": "springdoc.group-configs", "type": "java.util.List<com.stadium.config.SwaggerProperties$GroupConfig>", "sourceType": "com.stadium.config.SwaggerProperties"}, {"name": "springdoc.swagger-ui.config-url", "type": "java.lang.String", "description": "配置URL", "sourceType": "com.stadium.config.SpringDocProperties$SwaggerUi"}, {"name": "springdoc.swagger-ui.contact.email", "type": "java.lang.String", "sourceType": "com.stadium.config.SwaggerProperties$SwaggerUi$Contact"}, {"name": "springdoc.swagger-ui.contact.name", "type": "java.lang.String", "sourceType": "com.stadium.config.SwaggerProperties$SwaggerUi$Contact"}, {"name": "springdoc.swagger-ui.description", "type": "java.lang.String", "sourceType": "com.stadium.config.SwaggerProperties$SwaggerUi"}, {"name": "springdoc.swagger-ui.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用Swagger UI", "sourceType": "com.stadium.config.SpringDocProperties$SwaggerUi"}, {"name": "springdoc.swagger-ui.operations-sorter", "type": "java.lang.String", "description": "操作排序", "sourceType": "com.stadium.config.SpringDocProperties$SwaggerUi"}, {"name": "springdoc.swagger-ui.path", "type": "java.lang.String", "description": "Swagger UI路径", "sourceType": "com.stadium.config.SpringDocProperties$SwaggerUi"}, {"name": "springdoc.swagger-ui.show-common-extensions", "type": "java.lang.Bo<PERSON>an", "description": "是否显示通用扩展", "sourceType": "com.stadium.config.SpringDocProperties$SwaggerUi"}, {"name": "springdoc.swagger-ui.show-extensions", "type": "java.lang.Bo<PERSON>an", "description": "是否显示扩展", "sourceType": "com.stadium.config.SpringDocProperties$SwaggerUi"}, {"name": "springdoc.swagger-ui.tags-sorter", "type": "java.lang.String", "description": "标签排序", "sourceType": "com.stadium.config.SpringDocProperties$SwaggerUi"}, {"name": "springdoc.swagger-ui.title", "type": "java.lang.String", "sourceType": "com.stadium.config.SwaggerProperties$SwaggerUi"}, {"name": "springdoc.swagger-ui.version", "type": "java.lang.String", "sourceType": "com.stadium.config.SwaggerProperties$SwaggerUi"}, {"name": "stadium.backup.backup-dir", "type": "java.lang.String", "description": "备份文件存储目录", "sourceType": "com.stadium.config.BackupProperties"}, {"name": "stadium.backup.db-host", "type": "java.lang.String", "description": "数据库主机", "sourceType": "com.stadium.config.BackupProperties"}, {"name": "stadium.backup.db-name", "type": "java.lang.String", "description": "数据库名称", "sourceType": "com.stadium.config.BackupProperties"}, {"name": "stadium.backup.db-password", "type": "java.lang.String", "description": "数据库密码", "sourceType": "com.stadium.config.BackupProperties"}, {"name": "stadium.backup.db-port", "type": "java.lang.String", "description": "数据库端口", "sourceType": "com.stadium.config.BackupProperties"}, {"name": "stadium.backup.db-username", "type": "java.lang.String", "description": "数据库用户名", "sourceType": "com.stadium.config.BackupProperties"}, {"name": "stadium.backup.mysql-dump-path", "type": "java.lang.String", "description": "MySQL备份命令路径", "sourceType": "com.stadium.config.BackupProperties"}, {"name": "stadium.backup.mysql-path", "type": "java.lang.String", "description": "MySQL命令路径", "sourceType": "com.stadium.config.BackupProperties"}, {"name": "stadium.check-in.statistics.cache.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.stadium.config.StadiumProperties$CheckIn$Statistics$Cache"}, {"name": "stadium.check-in.statistics.cache.expiration", "type": "java.lang.Integer", "sourceType": "com.stadium.config.StadiumProperties$CheckIn$Statistics$Cache"}, {"name": "stadium.check-in.statistics.export.filename-prefix", "type": "java.lang.String", "sourceType": "com.stadium.config.StadiumProperties$CheckIn$Statistics$Export"}, {"name": "stadium.check-in.statistics.export.path", "type": "java.lang.String", "sourceType": "com.stadium.config.StadiumProperties$CheckIn$Statistics$Export"}, {"name": "stadium.check-in.statistics.task.update-interval", "type": "java.lang.Long", "sourceType": "com.stadium.config.StadiumProperties$CheckIn$Statistics$Task"}, {"name": "stadium.cors.allow-credentials", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.stadium.config.CorsProperties"}, {"name": "stadium.cors.allowed-headers", "type": "java.lang.String[]", "sourceType": "com.stadium.config.CorsProperties"}, {"name": "stadium.cors.allowed-methods", "type": "java.lang.String[]", "sourceType": "com.stadium.config.CorsProperties"}, {"name": "stadium.cors.allowed-origins", "type": "java.lang.String[]", "sourceType": "com.stadium.config.CorsProperties"}, {"name": "stadium.cors.exposed-headers", "type": "java.lang.String[]", "sourceType": "com.stadium.config.CorsProperties"}, {"name": "stadium.cors.max-age", "type": "java.lang.Long", "sourceType": "com.stadium.config.CorsProperties"}, {"name": "stadium.jwt.access-token-expiration", "type": "java.lang.Long", "description": "访问令牌过期时间（秒）", "sourceType": "com.stadium.config.JwtConfig"}, {"name": "stadium.jwt.refresh-token-expiration", "type": "java.lang.Long", "description": "刷新令牌过期时间（秒）", "sourceType": "com.stadium.config.JwtConfig"}, {"name": "stadium.jwt.secret", "type": "java.lang.String", "description": "密钥", "sourceType": "com.stadium.config.JwtConfig"}, {"name": "stadium.payment.internal.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用站内支付", "sourceType": "com.stadium.config.InternalPaymentConfig"}, {"name": "stadium.payment.internal.fee-rate", "type": "java.math.BigDecimal", "description": "手续费率", "sourceType": "com.stadium.config.InternalPaymentConfig"}, {"name": "stadium.payment.internal.min-balance", "type": "java.math.BigDecimal", "description": "最低余额要求", "sourceType": "com.stadium.config.InternalPaymentConfig"}, {"name": "stadium.swagger.contact.email", "type": "java.lang.String", "description": "联系人邮箱", "sourceType": "com.stadium.config.StadiumSwaggerProperties$Contact"}, {"name": "stadium.swagger.contact.name", "type": "java.lang.String", "description": "联系人姓名", "sourceType": "com.stadium.config.StadiumSwaggerProperties$Contact"}, {"name": "stadium.swagger.contact.url", "type": "java.lang.String", "description": "联系人网址", "sourceType": "com.stadium.config.StadiumSwaggerProperties$Contact"}, {"name": "stadium.swagger.description", "type": "java.lang.String", "description": "API描述", "sourceType": "com.stadium.config.StadiumSwaggerProperties"}, {"name": "stadium.swagger.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用Swagger", "sourceType": "com.stadium.config.StadiumSwaggerProperties"}, {"name": "stadium.swagger.license.name", "type": "java.lang.String", "description": "许可证名称", "sourceType": "com.stadium.config.StadiumSwaggerProperties$License"}, {"name": "stadium.swagger.license.url", "type": "java.lang.String", "description": "许可证网址", "sourceType": "com.stadium.config.StadiumSwaggerProperties$License"}, {"name": "stadium.swagger.title", "type": "java.lang.String", "description": "API标题", "sourceType": "com.stadium.config.StadiumSwaggerProperties"}, {"name": "stadium.swagger.version", "type": "java.lang.String", "description": "API版本", "sourceType": "com.stadium.config.StadiumSwaggerProperties"}, {"name": "stadium.upload.allowed-types", "type": "java.lang.String", "description": "允许的文件类型", "sourceType": "com.stadium.config.StadiumUploadProperties"}, {"name": "stadium.upload.max-size", "type": "java.lang.Long", "description": "最大文件大小（字节）", "sourceType": "com.stadium.config.StadiumUploadProperties"}, {"name": "stadium.upload.path", "type": "java.lang.String", "description": "上传文件保存路径", "sourceType": "com.stadium.config.StadiumUploadProperties"}, {"name": "stadium.venue.booking.status.cancelled", "type": "java.lang.Integer", "sourceType": "com.stadium.config.StadiumProperties$Venue$Booking$Status"}, {"name": "stadium.venue.booking.status.completed", "type": "java.lang.Integer", "sourceType": "com.stadium.config.StadiumProperties$Venue$Booking$Status"}, {"name": "stadium.venue.booking.status.paid", "type": "java.lang.Integer", "sourceType": "com.stadium.config.StadiumProperties$Venue$Booking$Status"}, {"name": "stadium.venue.booking.status.pending", "type": "java.lang.Integer", "sourceType": "com.stadium.config.StadiumProperties$Venue$Booking$Status"}, {"name": "stadium.venue.booking.status.refunded", "type": "java.lang.Integer", "sourceType": "com.stadium.config.StadiumProperties$Venue$Booking$Status"}, {"name": "stadium.venue.default-image", "type": "java.lang.String", "sourceType": "com.stadium.config.StadiumProperties$Venue"}, {"name": "stadium.venue.payment.methods.alipay", "type": "java.lang.Integer", "sourceType": "com.stadium.config.StadiumProperties$Venue$Payment$Methods"}, {"name": "stadium.venue.payment.methods.balance", "type": "java.lang.Integer", "description": "余额支付", "sourceType": "com.stadium.config.PaymentMethodConfig"}, {"name": "stadium.venue.payment.methods.points", "type": "java.lang.Integer", "description": "积分支付", "sourceType": "com.stadium.config.PaymentMethodConfig"}, {"name": "stadium.venue.payment.methods.wechat", "type": "java.lang.Integer", "sourceType": "com.stadium.config.StadiumProperties$Venue$Payment$Methods"}, {"name": "stadium.venue.rating.default-rating", "type": "java.lang.Integer", "sourceType": "com.stadium.config.StadiumProperties$Venue$Rating"}, {"name": "stadium.venue.rating.max", "type": "java.lang.Integer", "sourceType": "com.stadium.config.StadiumProperties$Venue$Rating"}, {"name": "stadium.venue.rating.min", "type": "java.lang.Integer", "sourceType": "com.stadium.config.StadiumProperties$Venue$Rating"}, {"name": "stadium.venue.refund.fee-rate", "type": "java.lang.Double", "sourceType": "com.stadium.config.StadiumProperties$Venue$Refund"}, {"name": "stadium.venue.refund.time-limit", "type": "java.lang.Integer", "sourceType": "com.stadium.config.StadiumProperties$Venue$Refund"}, {"name": "stadium.venue.statistics.time-range", "type": "java.lang.Integer", "sourceType": "com.stadium.config.StadiumProperties$Venue$Statistics"}, {"name": "stadium.venue.statistics.types.daily", "type": "java.lang.Integer", "sourceType": "com.stadium.config.StadiumProperties$Venue$Statistics$Types"}, {"name": "stadium.venue.statistics.types.monthly", "type": "java.lang.Integer", "sourceType": "com.stadium.config.StadiumProperties$Venue$Statistics$Types"}, {"name": "stadium.venue.statistics.types.weekly", "type": "java.lang.Integer", "sourceType": "com.stadium.config.StadiumProperties$Venue$Statistics$Types"}, {"name": "stadium.venue.statistics.types.yearly", "type": "java.lang.Integer", "sourceType": "com.stadium.config.StadiumProperties$Venue$Statistics$Types"}, {"name": "stadium.venue.status.available", "type": "java.lang.Integer", "sourceType": "com.stadium.config.StadiumProperties$Venue$Status"}, {"name": "stadium.venue.status.unavailable", "type": "java.lang.Integer", "sourceType": "com.stadium.config.StadiumProperties$Venue$Status"}, {"name": "task.execution.pool.allow-core-thread-timeout", "type": "java.lang.Bo<PERSON>an", "description": "是否允许核心线程超时", "sourceType": "com.stadium.config.TaskExecutionProperties$Pool"}, {"name": "task.execution.pool.core-size", "type": "java.lang.Integer", "description": "核心线程数", "sourceType": "com.stadium.config.TaskExecutionProperties$Pool"}, {"name": "task.execution.pool.keep-alive", "type": "java.lang.Integer", "description": "线程空闲超时时间（秒）", "sourceType": "com.stadium.config.TaskExecutionProperties$Pool"}, {"name": "task.execution.pool.max-size", "type": "java.lang.Integer", "description": "最大线程数", "sourceType": "com.stadium.config.TaskExecutionProperties$Pool"}, {"name": "task.execution.pool.queue-capacity", "type": "java.lang.Integer", "description": "队列容量", "sourceType": "com.stadium.config.TaskExecutionProperties$Pool"}, {"name": "task.execution.pool.thread-name-prefix", "type": "java.lang.String", "description": "线程名称前缀", "sourceType": "com.stadium.config.TaskExecutionProperties$Pool"}, {"name": "upload.allowed-types", "type": "java.lang.String", "description": "允许的文件类型，多个用逗号分隔", "sourceType": "com.stadium.config.FileUploadConfig"}, {"name": "upload.avatar-path", "type": "java.lang.String", "description": "头像保存路径", "sourceType": "com.stadium.config.FileUploadConfig"}, {"name": "upload.image-path", "type": "java.lang.String", "description": "图片保存路径", "sourceType": "com.stadium.config.FileUploadConfig"}, {"name": "upload.max-size", "type": "java.lang.String", "description": "最大文件大小", "sourceType": "com.stadium.config.FileUploadConfig"}, {"name": "upload.path", "type": "java.lang.String", "description": "上传文件保存路径", "sourceType": "com.stadium.config.FileUploadConfig"}], "hints": []}