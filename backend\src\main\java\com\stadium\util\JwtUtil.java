package com.stadium.util;

import com.stadium.config.JwtProperties;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import javax.crypto.SecretKey;
import java.util.Date;
import java.util.function.Function;

/**
 * JWT工具类
 */
@Slf4j
@Component
public class JwtUtil {

    private final JwtProperties jwtProperties;
    private SecretKey key;

    public JwtUtil(JwtProperties jwtProperties) {
        this.jwtProperties = jwtProperties;
    }

    @PostConstruct
    public void init() {
        this.key = Keys.hmacShaKeyFor(jwtProperties.getSecret().getBytes());
    }

    public String extractUsername(String token) {
        return extractClaim(token, Claims::getSubject);
    }

    public Date extractExpiration(String token) {
        return extractClaim(token, Claims::getExpiration);
    }

    public <T> T extractClaim(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = extractAllClaims(token);
        return claimsResolver.apply(claims);
    }

    public Claims extractAllClaims(String token) {
        try {
            return Jwts.parserBuilder()
                    .setSigningKey(key)
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
        } catch (Exception e) {
            log.error("解析token失败", e);
            throw e;
        }
    }

    private Boolean isTokenExpired(String token) {
        return extractExpiration(token).before(new Date());
    }

    /**
     * 生成访问令牌
     *
     * @param userId 用户ID
     * @return 访问令牌
     */
    public String generateAccessToken(Long userId) {
        return generateToken(userId, jwtProperties.getAccessTokenExpiration());
    }

    /**
     * 生成刷新令牌
     *
     * @param userId 用户ID
     * @return 刷新令牌
     */
    public String generateRefreshToken(Long userId) {
        return generateToken(userId, jwtProperties.getRefreshTokenExpiration());
    }

    /**
     * 生成令牌
     *
     * @param userId     用户ID
     * @param expiration 过期时间（秒）
     * @return 令牌
     */
    private String generateToken(Long userId, Long expiration) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expiration * 1000);

        return Jwts.builder()
                .subject(String.valueOf(userId))
                .issuedAt(now)
                .expiration(expiryDate)
                .signWith(key)
                .compact();
    }

    public Boolean validateToken(String token, UserDetails userDetails) {
        final String username = extractUsername(token);
        return (username.equals(userDetails.getUsername()) && !isTokenExpired(token));
    }

    /**
     * 为UserDetails生成令牌
     *
     * @param userDetails 用户详情
     * @return 令牌
     */
    public String generateToken(UserDetails userDetails) {
        String username = userDetails.getUsername();
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + jwtProperties.getAccessTokenExpiration() * 1000);

        log.info("为用户 {} 生成令牌，过期时间：{}", username, expiryDate);
        log.info("UserDetails类型: {}", userDetails.getClass().getName());

        if (userDetails.getClass().getName().endsWith("CustomUserDetails")) {
            try {
                Object user = userDetails.getClass().getMethod("getUser").invoke(userDetails);
                Long userId = (Long) user.getClass().getMethod("getId").invoke(user);
                log.info("从CustomUserDetails获取的用户ID: {}", userId);
            } catch (Exception e) {
                log.error("获取用户ID失败: {}", e.getMessage(), e);
            }
        }

        String token = Jwts.builder()
                .subject(username)
                .issuedAt(now)
                .expiration(expiryDate)
                .signWith(key)
                .compact();

        log.info("生成的令牌：{}", token);

        return token;
    }

    /**
     * 从token中获取用户名
     */
    public String getUsernameFromToken(String token) {
        return extractUsername(token);
    }

    /**
     * 从刷新令牌中获取用户名
     *
     * @param refreshToken 刷新令牌
     * @return 用户名
     */
    public String getUsernameFromRefreshToken(String refreshToken) {
        try {
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(key)
                    .build()
                    .parseClaimsJws(refreshToken)
                    .getBody();
            return claims.getSubject();
        } catch (Exception e) {
            log.error("解析刷新令牌失败", e);
            return null;
        }
    }

    public boolean canTokenBeRefreshed(String token) {
        return !isTokenExpired(token);
    }

    /**
     * 检查令牌是否在黑名单中
     *
     * @param token 令牌
     * @return 是否在黑名单中
     */
    public boolean isTokenBlacklisted(String token) {
        // 在实际应用中，应该通过Redis或其他缓存来检查token是否在黑名单中
        // 此处为简化实现，仅检查token是否过期
        try {
            return isTokenExpired(token);
        } catch (Exception e) {
            log.error("检查token是否在黑名单中失败", e);
            return true;
        }
    }

    /**
     * 将令牌添加到黑名单中
     *
     * @param token 令牌
     */
    public void addToBlacklist(String token) {
        // 在实际应用中，应该将token添加到Redis或其他缓存中的黑名单
        // 此处为简化实现，仅记录日志
        log.info("将token添加到黑名单中: {}", token);
        // 实际实现应该调用Redis服务
        // redisTemplate.opsForValue().set("token:blacklist:" + token, "1",
        // getExpirationFromToken(token), TimeUnit.MILLISECONDS);
    }
}