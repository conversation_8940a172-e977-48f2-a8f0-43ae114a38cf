package com.stadium;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * 版本兼容性测试
 * 验证Spring Boot 3.x和Java 11的兼容性
 */
@SpringBootTest
@ActiveProfiles("test")
public class VersionCompatibilityTest {

    @Test
    public void contextLoads() {
        // 如果Spring上下文能够成功加载，说明基本兼容性没有问题
        System.out.println("Spring Boot 3.x 上下文加载成功！");
    }

    @Test
    public void testJavaVersion() {
        String javaVersion = System.getProperty("java.version");
        System.out.println("当前Java版本: " + javaVersion);
        
        // 验证Java版本是否为11或更高
        String[] versionParts = javaVersion.split("\\.");
        int majorVersion = Integer.parseInt(versionParts[0]);
        
        if (majorVersion >= 11) {
            System.out.println("Java版本兼容性检查通过！");
        } else {
            throw new RuntimeException("Java版本过低，需要Java 11或更高版本");
        }
    }

    @Test
    public void testJakartaEE() {
        try {
            // 验证Jakarta EE包是否可用
            Class.forName("jakarta.servlet.http.HttpServletRequest");
            Class.forName("jakarta.validation.constraints.NotNull");
            Class.forName("jakarta.annotation.PostConstruct");
            
            System.out.println("Jakarta EE 迁移检查通过！");
        } catch (ClassNotFoundException e) {
            throw new RuntimeException("Jakarta EE 包未找到，迁移可能不完整", e);
        }
    }
}
